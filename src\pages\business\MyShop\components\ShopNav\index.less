.phone {
  display: flex;
  flex-direction: column;
  width: 375px;
  min-height: 632px;
  box-shadow: 0 0 14px 0 rgba(0, 0, 0, 0.1);
  :global {
    .stats,
    .nav,
    .tab,
    .tabRudder {
      width: 100%;
    }
    .nav {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 44px;
      padding: 0 20px;
      color: #14131f;
      font-weight: 600;
      font-size: 18px;
      background: #fff;
      border-bottom: 1px solid #e4e4e4;
      > img {
        width: 18px;
      }
    }
    .tab {
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      height: 50px;
      margin-top: auto;
      border-top: 1px solid #e4e4e4;
      > div {
        display: flex;
        flex-direction: column;
        align-items: center;
        > img,
        > svg {
          width: 30px;
          height: 30px;
        }
        .iconPlaceholder {
          width: 30px;
          height: 30px;
          background-color: #f3f3f3;
          border: 1px dashed #ddd;
          border-radius: 6px;
        }
        > span {
          color: #14131f;
          font-size: 11px;
        }
      }
    }
    .tabRudder {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 80px;
      margin-top: auto;
      padding: 0 25px;
      /* 参考图的背景条 */
      background: url(https://lanhu-oss-proxy.lanhuapp.com/SketchPng42e854a063002b9ca64be275b35088422c66b77b984747c808e626785ffd5bfd)
        center center / 100% 100% no-repeat;
    }
  }
}
.navList {
  :global {
    .ant-form-item-control-input-content > div {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      width: 100% !important;
      > div {
        width: auto;
        // .ant-input-affix-wrapper {
        //   width: 160px;
        // }
        .ant-btn {
          width: 100%;
        }
      }
      > button {
        width: 251.05px;
        height: 269px;
      }
    }
  }
}
.icon {
  margin-bottom: 0;
  :global {
    /* 覆盖 .navList 中对 control content 的通用布局，避免影响图标块 */
    .ant-form-item-control-input-content > div {
      display: inline-flex;
      flex-wrap: nowrap;
      gap: 0;
      width: auto !important;
      align-items: flex-start;
    }
    .ant-upload.ant-upload-select-picture-card,
    .ant-upload-list-picture-card-container {
      width: 48px;
      height: 48px;
      margin: 0;
    }
    .ant-upload-list-item {
      padding: 0;
    }
    .ant-form-item {
      margin-bottom: 0;
    }
  }
}
.iconContainer {
  display: inline-block;
}
.iconWrap {
  display: inline-flex;
  flex-direction: column;
  align-items: stretch;
}

/* 舵式预览：左右两侧的小图标+文字 */
.rudderSideItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 50px;
}
.rudderIconSmall {
  width: 30px;
  height: 30px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  > svg {
    width: 30px;
    height: 30px;
    display: block;
  }
}
.rudderIconPlaceholderSmall {
  width: 23px;
  height: 23px;
  background-color: #f3f3f3;
  border: 1px dashed #ddd;
  border-radius: 6px;
}
.rudderLabel {
  color: #14131f;
  font-size: 11px;
  line-height: 16px;
  margin-top: 3px;
}
.rudderLabelActive {
  color: #349fff;
}

/* 舵式预览：中间的圆形容器 */
.rudderCenterItem {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url(https://lanhu-oss-proxy.lanhuapp.com/SketchPnge123935d749e9261914f7672e281042af75cd1f32167bb86e6ed225a165a109d)
    center center / 100% 100% no-repeat;
}
.rudderCenterIcon {
  width: 37px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  > svg {
    width: 37px;
    height: 40px;
    display: block;
  }
}
.addWrap {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
}
.iconBlock {
  position: relative;
  width: 200px;
  padding: 12px 0;
  background: #f7f7f7;
  border-radius: 8px;
  cursor: pointer;
}
.addBlock {
  width: 176px;
  height: 97px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
}
.addIcon {
  color: #bfbfbf;
  font-size: 28px;
}
.addTextInside {
  margin-top: 8px;
  color: #bfbfbf;
  font-size: 12px;
}
.iconError {
  width: 176px;
  margin-top: 6px;
  color: #ff4d4f;
  font-size: 12px;
  line-height: 1.6;
  text-align: center;
}
.addText {
  margin-top: 8px;
  color: #bfbfbf;
  font-size: 12px;
}
.iconsRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 中间位置导航的单个图标居中布局 */
.centerIconRow {
  display: flex;
  align-items: center;
  justify-content: center;
}
.circle_img{
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.circle {
  width: 50%;
  display: flex;
  justify-content: center;
}

.labelsRow {
  display: flex;
  justify-content: space-between;
  color: #aaa;
  font-size: 12px;
  margin-top: 8px;
  span {
    width: 50%;
    text-align: center;
  }
}
.mask {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 32px;
  background: rgba(0, 0, 0, 0.45);
  color: #fff;
  text-align: center;
  line-height: 32px;
  border-radius: 0 0 8px 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
  cursor: pointer;
}
.iconBlock:hover .mask {
  opacity: 1;
}
.iconList {
  display: flex;
  flex-direction: column;
  gap: 40px;
  > div {
    display: flex;
    flex-direction: column;
    gap: 20px;
    > span {
      color: #ff4949;
      font-weight: bold;
      font-size: 28px;
      line-height: 40px;
    }
    > div {
      display: flex;
      flex-wrap: wrap;
      gap: 40px;
      > div {
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-items: center;
        > div {
          position: relative;
          display: flex;
          gap: 8px;
          cursor: pointer;
          &:hover::after {
            position: absolute;
            border: 1px dashed #ddd;
            content: '';
            inset: 0;
          }
        }
        > span {
          color: #14131f;
          font-size: 22px;
        }
      }
    }
  }
}

/* Placeholder for empty nav icon */
.iconPlaceholder {
  width: 30px;
  height: 30px;
  background-color: #f3f3f3;
  border: 1px dashed #ddd;
  border-radius: 6px;
}

/* 保留：rudderTab 已被 tabRudder 结构替换，不再使用 */
