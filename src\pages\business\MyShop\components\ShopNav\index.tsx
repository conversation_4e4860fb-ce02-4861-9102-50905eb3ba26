import closeIcon from '@/assets/design_close.svg';
import menuIcon from '@/assets/design_menu.svg';
import statusIcon from '@/assets/design_status.jpg';
import ProSvg from '@/common/components/ProSvg';
import type { ProFormInstance, FormListActionType } from '@ant-design/pro-components';
import { ProCard, ProForm, ProFormRadio } from '@ant-design/pro-components';
import { Space, Button, Popconfirm, message, Form } from 'antd';
import { FooterToolbar } from '@ant-design/pro-layout';
import IconSelectModal from './IconSelectModal';
import { useEffect, useRef, useState } from 'react';
import styles from './index.less';
import CommonNavForm from './CommonNavForm';
import RudderNavForm from './RudderNavForm';

type ShopNavItem = {
  name: string;
  icon: string;
  icon_active: string;
  link?: string;
  linkType?: 'internal' | 'external';
};

// 中间导航项的默认图片
const DEFAULT_CENTER_ICON = 'https://prod.shukeyun.com/maintenance/deepfile/data/2025-08-15/upload_1e3fcd92856e16adc98e068f742d2b8d.png';

const DEFAULT_COMMON: ShopNavItem[] = [
  { name: '首页', link: 'home', icon: '', icon_active: '' },
  { name: '导览', link: 'tour', icon: '', icon_active: '' },
  { name: '订单', link: 'order', icon: '', icon_active: '' },
  { name: '我的', link: 'my', icon: '', icon_active: '' },
];

export default ({ shopConfig, updateShopConfig, storeId }: any) => {
  const formRef = useRef<ProFormInstance>();
  const listActionRef = useRef<FormListActionType>();
  const [tabList, setTabList] = useState<any>([]);
  const [isModalOpen, setIsModalOpen] = useState(-1);
  const MAX_NAV = 5;
  const [commonNavDraft, setCommonNavDraft] = useState<any[]>([]);
  const [rudderNavDraft, setRudderNavDraft] = useState<any[]>([]);
  const [rudderCount, setRudderCount] = useState<number>(3);
  const [currentNavStyle, setCurrentNavStyle] = useState<'common' | 'rudder'>('common');
  const iconList = ['通用模版', '乐园类景区', '自然类景区', '文化类景区'].map((value, index) => ({
    title: value,
    list: ['首页', '导览', '订单', '我的'].map((listValue, listIndex) => ({
      name: listValue,
      icon: `${index + 1}_${listIndex + 1}`,
      icon_active: `${index + 1}_${listIndex + 1}_`,
    })),
  }));
  useEffect(() => {
    if (!shopConfig) return;
    const initialStyle = shopConfig?.navStyle ?? 'common';
    formRef.current?.setFieldValue('navStyle', initialStyle);
    setCurrentNavStyle(initialStyle);
    if (initialStyle === 'rudder') {
      const count =
        shopConfig?.shopNav?.length === 3 || shopConfig?.shopNav?.length === 5
          ? shopConfig.shopNav.length
          : 3;
      const rudder = shopConfig?.shopNav?.length
        ? resizeRudderList(shopConfig.shopNav, count)
        : buildRudderDefault(count);
      const common = DEFAULT_COMMON;
      setRudderCount(count);
      setRudderNavDraft(rudder);
      setCommonNavDraft(common);
      formRef.current?.setFieldsValue({ rudderCount: count, shopNav: rudder });
      setTabList(rudder);
    } else {
      const common = shopConfig?.shopNav?.length ? shopConfig.shopNav : DEFAULT_COMMON;
      const count = 3;
      const rudder = buildRudderDefault(count);
      setRudderCount(count);
      setCommonNavDraft(common);
      setRudderNavDraft(rudder);
      formRef.current?.setFieldsValue({ shopNav: common });
      setTabList(common);
    }
  }, [shopConfig]);

  const getNavName = (item: any, index: number) => {
    const name = item?.name;
    return typeof name === 'string' && name.trim() ? name : `导航${index + 1}`;
  };
  const renderIcon = (src?: string) => {
    if (!src) return <div className={styles.iconPlaceholder} />;
    return typeof src === 'string' && src.includes('http') ? (
      <img src={src} />
    ) : (
      <ProSvg src={src} color={shopConfig.shopStyle?.color} />
    );
  };

  const ensureRudderNavList = (list: ShopNavItem[], count: number): ShopNavItem[] => {
    const next: ShopNavItem[] = (list || []).slice(0, count);
    while (next.length < count) {
      next.splice(Math.max(next.length - 1, 0), 0, { name: '', icon: '', icon_active: '' });
    }
    return next;
  };

  const buildRudderDefault = (count: number): ShopNavItem[] => {
    const target = Math.max(count, 3);
    const result: ShopNavItem[] = new Array(target)
      .fill(null)
      .map(() => ({ name: '', icon: '', icon_active: '' }));
    const mid = Math.floor(target / 2);
    result[0] = { name: '首页', link: 'home', icon: '', icon_active: '' };
    result[mid] = {
      name: '尚品工坊',
      link: undefined as any,
      linkType: 'internal',
      icon: '',
      icon_active: DEFAULT_CENTER_ICON,
    };
    result[target - 1] = { name: '我的', link: 'my', icon: '', icon_active: '' };
    return result.slice(0, count);
  };
  const resizeRudderList = (currentList: ShopNavItem[], count: number): ShopNavItem[] => {
    const target = Math.max(count, 3);
    const result: ShopNavItem[] = new Array(target)
      .fill(null)
      .map(() => ({ name: '', icon: '', icon_active: '' }));
    const oldFirst: ShopNavItem = currentList?.[0] ?? {
      name: '首页',
      link: 'home',
      icon: '',
      icon_active: '',
    };
    const oldMid: ShopNavItem = currentList?.[Math.floor((currentList?.length || 3) / 2)] ?? {
      name: '尚品工坊',
      linkType: 'internal',
      icon: '',
      icon_active: DEFAULT_CENTER_ICON,
    };
    const oldLast: ShopNavItem = currentList?.[(currentList?.length || 1) - 1] ?? {
      name: '我的',
      link: 'my',
      icon: '',
      icon_active: '',
    };
    const mid = Math.floor(target / 2);
    result[0] = { ...oldFirst };
    result[mid] = { ...oldMid };
    result[target - 1] = { ...oldLast };
    // 若扩容（如 3->5），在 1..target-2 且不等于 mid 的位置填充空位，符合“2 和 4 填充”的业务；
    // 若等长，保留原有中间位
    if ((currentList?.length || 0) === target) {
      for (let i = 1; i < target - 1; i += 1) {
        if (i === mid) continue;
        result[i] = currentList[i] ?? { name: '', icon: '', icon_active: '' };
      }
    }
    return result.slice(0, count);
  };

  return (
    <>
      <ProCard split="vertical">
        <ProCard colSpan="447px">
          <div className={styles.phone}>
            <img className="stats" src={statusIcon} />
            <div className="nav">
              <img src={closeIcon} />
              <span>{tabList.length ? getNavName(tabList[0], 0) : ''}</span>
              <img src={menuIcon} />
            </div>
            {currentNavStyle === 'rudder' ? (
              <div className="tabRudder">
                <div className={styles.rudderSideItem}>
                  {(() => {
                    const left = tabList?.[0] || ({} as any);
                    const leftImg = left?.icon_active || left?.icon;
                    return leftImg ? (
                      typeof leftImg === 'string' && leftImg.includes('http') ? (
                        <img className={styles.rudderIconSmall} src={leftImg} />
                      ) : (
                        <div className={styles.rudderIconSmall}>
                          <ProSvg src={leftImg} color={shopConfig.shopStyle?.color} />
                        </div>
                      )
                    ) : (
                      <div className={styles.rudderIconPlaceholderSmall} />
                    );
                  })()}
                  <span className={`${styles.rudderLabel} ${styles.rudderLabelActive}`}>
                    {getNavName(tabList?.[0] || {}, 0)}
                  </span>
                </div>

                <div className={styles.rudderCenterItem}>
                  {(() => {
                    const midIndex = Math.floor(Math.max(tabList?.length || 3, 3) / 2);
                    const mid = tabList?.[midIndex] || ({} as any);
                    const midImg = mid?.icon_active || mid?.icon;
                    return midImg ? (
                      typeof midImg === 'string' && midImg.includes('http') ? (
                        <img className={styles.rudderCenterIcon} src={midImg} />
                      ) : (
                        <div className={styles.rudderCenterIcon}>
                          <ProSvg src={midImg} color={shopConfig.shopStyle?.color} />
                        </div>
                      )
                    ) : (
                      <div className={styles.iconPlaceholder} />
                    );
                  })()}
                </div>

                <div className={styles.rudderSideItem}>
                  {(() => {
                    const last = tabList?.[(tabList?.length || 1) - 1] || ({} as any);
                    const rightImg = last?.icon || last?.icon_active;
                    return rightImg ? (
                      typeof rightImg === 'string' && rightImg.includes('http') ? (
                        <img className={styles.rudderIconSmall} src={rightImg} />
                      ) : (
                        <div className={styles.rudderIconSmall}>
                          <ProSvg src={rightImg} color={shopConfig.shopStyle?.color} />
                        </div>
                      )
                    ) : (
                      <div className={styles.rudderIconPlaceholderSmall} />
                    );
                  })()}
                  <span className={styles.rudderLabel}>
                    {getNavName(
                      tabList?.[(tabList?.length || 1) - 1] || {},
                      (tabList?.length || 1) - 1,
                    )}
                  </span>
                </div>
              </div>
            ) : (
              <div className="tab">
                {tabList.map((item: any, index: any) => {
                  const img = item[index ? 'icon' : 'icon_active'];
                  return (
                    <div
                      key={`${item?.name || 'nav'}-${item?.icon || 'i'}-${
                        item?.icon_active || 'ia'
                      }`}
                    >
                      {img ? (
                        img.includes('http') ? (
                          <img src={img} />
                        ) : (
                          <ProSvg src={img} color={shopConfig.shopStyle?.color} />
                        )
                      ) : (
                        <div className={styles.iconPlaceholder} />
                      )}
                      <span style={index ? {} : { color: shopConfig.shopStyle?.color }}>
                        {getNavName(item, index)}
                      </span>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </ProCard>
        <ProCard headStyle={{ paddingRight: 0 }} bodyStyle={{ paddingRight: 0 }} headerBordered>
          <ProForm
            formRef={formRef}
            layout="horizontal"
            onValuesChange={(changedValues: any, values: Record<string, any>) => {
              // 优先处理样式切换，避免覆盖另一套草稿
              if (Object.prototype.hasOwnProperty.call(changedValues, 'navStyle')) {
                const style = changedValues.navStyle as 'common' | 'rudder';
                setCurrentNavStyle(style);
                if (style === 'rudder') {
                  const count = formRef.current?.getFieldValue('rudderCount') ?? rudderCount;
                  const list = rudderNavDraft?.length
                    ? resizeRudderList(rudderNavDraft, count)
                    : buildRudderDefault(count);
                  formRef.current?.setFieldsValue({ rudderCount: count, shopNav: list });
                  setTabList(list);
                } else {
                  const list = commonNavDraft?.length ? commonNavDraft : DEFAULT_COMMON;
                  formRef.current?.setFieldsValue({ shopNav: list });
                  setTabList(list);
                }
                return;
              }
              // 仅在 shopNav 变化时更新对应草稿
              if (Object.prototype.hasOwnProperty.call(changedValues, 'shopNav')) {
                const style = formRef.current?.getFieldValue('navStyle');
                const list = values.shopNav;
                setTabList(list);
                if (style === 'common') setCommonNavDraft(list);
                if (style === 'rudder') setRudderNavDraft(list);
              }
            }}
            onFinish={async (values) => {
              try {
                await updateShopConfig(values);
                const style = values?.navStyle;
                if (style === 'common') {
                  const reset = buildRudderDefault(rudderCount);
                  setRudderNavDraft(reset);
                } else if (style === 'rudder') {
                  setCommonNavDraft(DEFAULT_COMMON);
                }
                message.success('保存成功');
              } catch (e) {
                message.error('保存失败，请重试');
              }
            }}
            onFinishFailed={() => message.error('保存失败，请完善信息')}
            submitter={false}
          >
            <ProFormRadio.Group
              name="navStyle"
              label="导航样式"
              initialValue={shopConfig?.navStyle ?? 'common'}
              options={[
                { label: '通用样式', value: 'common' },
                { label: '舵式样式', value: 'rudder' },
              ]}
            />
            <Form.Item noStyle shouldUpdate>
              {(form) => {
                const style = form.getFieldValue('navStyle');
                return style === 'rudder' ? (
                  <RudderNavForm
                    formRef={formRef as any}
                    listActionRef={listActionRef as any}
                    tabList={tabList}
                    setTabList={setTabList}
                    setIsModalOpen={setIsModalOpen}
                    color={shopConfig.shopStyle?.color}
                    currentCount={rudderCount}
                    onCountChange={(count, nextList) => {
                      setRudderCount(count);
                      setRudderNavDraft(nextList);
                      form.setFieldsValue({ rudderCount: count, shopNav: nextList });
                      setTabList(nextList);
                    }}
                  />
                ) : (
                  <CommonNavForm
                    formRef={formRef as any}
                    listActionRef={listActionRef as any}
                    tabList={tabList}
                    setTabList={setTabList}
                    setIsModalOpen={setIsModalOpen}
                    MAX_NAV={MAX_NAV}
                    color={shopConfig.shopStyle?.color}
                  />
                );
              }}
            </Form.Item>
          </ProForm>
        </ProCard>
      </ProCard>
      <IconSelectModal
        open={isModalOpen != -1}
        onCancel={() => setIsModalOpen(-1)}
        iconList={iconList}
        color={shopConfig.shopStyle?.color}
        storeId={shopConfig?.storeId || storeId}
        onSelect={(listValue: any) => {
          setTabList((value: any) => {
            const valueClone = structuredClone(value);
            const style = formRef.current?.getFieldValue('navStyle');
            const midIndex = Math.floor(Math.max(valueClone?.length || 3, 3) / 2);
            if (style === 'rudder' && isModalOpen === midIndex) {
              valueClone[isModalOpen].icon_active = listValue.icon_active;
              valueClone[isModalOpen].icon = '';
            } else {
              [valueClone[isModalOpen].icon, valueClone[isModalOpen].icon_active] = [
                listValue.icon,
                listValue.icon_active,
              ];
            }
            formRef?.current?.setFieldsValue({
              shopNav: valueClone,
            });
            const currentStyle = formRef.current?.getFieldValue('navStyle');
            if (currentStyle === 'common') setCommonNavDraft(valueClone);
            if (currentStyle === 'rudder') setRudderNavDraft(valueClone);
            setIsModalOpen(-1);
            return valueClone;
          });
        }}
      />
      <FooterToolbar>
        <Space>
          <Popconfirm
            title="确认要重置吗？"
            okText="确定"
            cancelText="取消"
            onConfirm={() => {
              formRef.current?.setFieldsValue({ shopNav: shopConfig?.shopNav });
              setTabList(shopConfig?.shopNav || []);
            }}
          >
            <Button>重置</Button>
          </Popconfirm>
          <Button type="primary" onClick={() => formRef.current?.submit?.()}>
            保存
          </Button>
        </Space>
      </FooterToolbar>
    </>
  );
};
