import ProSvg from '@/common/components/ProSvg';
import { systemPageEnum } from '@/common/utils/enum';
import type { ProFormInstance, FormListActionType } from '@ant-design/pro-components';
import { ProFormList, ProFormText, ProFormSelect, ProFormRadio, ProForm, ProCard } from '@ant-design/pro-components';
import { Space, Form } from 'antd';
import type { FC, MutableRefObject } from 'react';
import styles from './index.less';

type RudderNavFormProps = {
  formRef: MutableRefObject<ProFormInstance | undefined>;
  listActionRef: MutableRefObject<FormListActionType | undefined>;
  tabList: any[];
  setTabList: (updater: any) => void;
  setIsModalOpen: (index: number) => void;
  color?: string;
  currentCount?: number;
  onCountChange?: (count: number, nextList: any[]) => void;
};

const ensureRudderNavList = (list: any[], count: number) => {
  const next = (list || []).slice(0, count);
  while (next.length < count) {
    next.splice(Math.max(next.length - 1, 0), 0, { name: '', icon: '', icon_active: '' });
  }
  // 不向数据写入 isCenter，避免污染提交数据
  return next;
};

// 根据目标数量重排舵式列表：固定首/中/尾，其余位置留空或在等长时保留
const adjustRudderByCount = (currentList: any[], count: number) => {
  const target = Math.max(count, 3);
  const result = new Array(target)
    .fill(null)
    .map(() => ({ name: '', icon: '', icon_active: '' }));
  const oldFirst = currentList?.[0] ?? { name: '首页', icon: '', icon_active: '' };
  const oldMid = currentList?.[Math.floor((currentList?.length || 3) / 2)] ?? {
    name: '尚品工坊',
    icon: '',
    icon_active: '',
  };
  const oldLast = currentList?.[(currentList?.length || 1) - 1] ?? {
    name: '我的',
    icon: '',
    icon_active: '',
  };
  const mid = Math.floor(target / 2);
  result[0] = { ...oldFirst };
  result[mid] = { ...oldMid };
  result[target - 1] = { ...oldLast };
  // 如果等长，保留非首/中/尾的原位内容；扩容时这些位为空（满足 3->5 填充在 1 和 3）
  if ((currentList?.length || 0) === target) {
    for (let i = 1; i < target - 1; i += 1) {
      if (i === mid) continue;
      result[i] = currentList[i] ?? { name: '', icon: '', icon_active: '' };
    }
  }
  return result;
};

const RudderNavForm: FC<RudderNavFormProps> = ({
  formRef,
  listActionRef,
  tabList,
  setTabList,
  setIsModalOpen,
  color,
  currentCount,
  onCountChange,
}) => {
  const renderIcon = (src?: string) => {
    if (!src) return <div className={styles.iconPlaceholder} />;
    return typeof src === 'string' && src.includes('http') ? (
      <img src={src} />
    ) : (
      <ProSvg src={src} color={color} />
    );
  };

  return (
    <>
      <ProFormRadio.Group
        name="rudderCount"
        label="导航数量"
        options={[
          { label: '3个', value: 3 },
          { label: '5个', value: 5 },
        ]}
        initialValue={currentCount ?? 3}
        fieldProps={{
          onChange: (e) => {
            const count = e.target.value;
            const current = formRef.current?.getFieldValue('shopNav') || [];
            const next = adjustRudderByCount(current, count);
            formRef.current?.setFieldsValue({ shopNav: next });
            setTabList(next);
            onCountChange?.(count, next);
          },
        }}
      />
      <Form.Item noStyle shouldUpdate>
        {(form) => {
          const count = form.getFieldValue('rudderCount') ?? 3;
          const mid = Math.floor(((tabList?.length || count) as number) / 2);
          return (
            <ProFormList
              label="导航设置"
              className={styles.navList}
              name="shopNav"
              creatorButtonProps={false}
              actionRef={listActionRef as any}
              min={count}
              max={count}
              copyIconProps={false}
              actionRender={() => []}
              itemRender={({ listDom, action }, { index }) => (
                <ProCard
                  bordered
                  title={<div style={{ lineHeight: '32px' }}>{`导航 ${index + 1}`}</div>}
                  headStyle={{ padding: '8px 24px' }}
                  extra={action}
                  type="inner"
                >
                  {listDom}
                </ProCard>
              )}
            >
              {(_, index) => {
                const isCenter = index === mid;
                return (
                  <>
                    <div style={{ display: 'flex', alignItems: 'flex-start', gap: 16 }}>
                      <ProForm.Item className={styles.icon} label={false}>
                        <Space align="start">
                          {isCenter ? (
                            <>
                              <ProFormText
                                name="icon_active"
                                hidden
                                rules={[{ required: true, message: '请添加图标' }]}
                              />
                              {!tabList?.[index]?.icon_active ? (
                                <div className={styles.addWrap}>
                                  <div
                                    className={styles.addBlock}
                                    onClick={() => setIsModalOpen(index)}
                                  >
                                    <div className={styles.addTextInside}>添加图标</div>
                                  </div>
                                  <Form.Item noStyle shouldUpdate>
                                    {(form2) => {
                                      const iconActiveErrors = form2.getFieldError([
                                        'shopNav',
                                        index,
                                        'icon_active',
                                      ]);
                                      const hasErrors = (iconActiveErrors?.length || 0) > 0;
                                      return hasErrors ? (
                                        <div className={styles.iconError}>请添加图标</div>
                                      ) : null;
                                    }}
                                  </Form.Item>
                                </div>
                              ) : (
                                <div className={styles.iconWrap}>
                                  <div
                                    className={styles.iconBlock}
                                    onClick={() => setIsModalOpen(index)}
                                  >
                                    <div className={styles.iconsRow}>
                                      <div className={`${styles.circle}`}>
                                        <div className={`${styles.circle_img}`}>
                                          {renderIcon(tabList?.[index]?.icon_active)}
                                        </div>
                                      </div>
                                    </div>
                                    <div className={styles.mask}>替换</div>
                                  </div>
                                </div>
                              )}
                            </>
                          ) : (
                            <>
                              <ProFormText
                                name="icon_active"
                                hidden
                                rules={[{ required: true, message: '请添加图标' }]}
                              />
                              <ProFormText
                                name="icon"
                                hidden
                                rules={[{ required: true, message: '请添加图标' }]}
                              />
                              {!(tabList?.[index]?.icon_active || tabList?.[index]?.icon) ? (
                                <div className={styles.addWrap}>
                                  <div
                                    className={styles.addBlock}
                                    onClick={() => setIsModalOpen(index)}
                                  >
                                    <div className={styles.addTextInside}>添加图标</div>
                                  </div>
                                  <Form.Item noStyle shouldUpdate>
                                    {(form2) => {
                                      const iconErrors = form2.getFieldError([
                                        'shopNav',
                                        index,
                                        'icon',
                                      ]);
                                      const iconActiveErrors = form2.getFieldError([
                                        'shopNav',
                                        index,
                                        'icon_active',
                                      ]);
                                      const hasErrors =
                                        (iconErrors?.length || 0) > 0 ||
                                        (iconActiveErrors?.length || 0) > 0;
                                      return hasErrors ? (
                                        <div className={styles.iconError}>请添加图标</div>
                                      ) : null;
                                    }}
                                  </Form.Item>
                                </div>
                              ) : (
                                <div className={styles.iconWrap}>
                                  <div
                                    className={styles.iconBlock}
                                    onClick={() => setIsModalOpen(index)}
                                  >
                                    <div className={styles.iconsRow}>
                                      <div className={`${styles.circle}`}>
                                        <div className={`${styles.circle_img}`}>
                                          {renderIcon(tabList?.[index]?.icon_active)}
                                        </div>
                                      </div>
                                      <div className={styles.circle}>
                                        <div className={`${styles.circle_img}`}>
                                          {renderIcon(tabList?.[index]?.icon)}
                                        </div>
                                      </div>
                                    </div>
                                    <div className={styles.mask}>替换</div>
                                  </div>
                                  <div className={styles.labelsRow}>
                                    <span>选中</span>
                                    <span>未选中</span>
                                  </div>
                                </div>
                              )}
                            </>
                          )}
                        </Space>
                      </ProForm.Item>
                      <div style={{ display: 'flex', flexDirection: 'column', marginTop: 16 }}>
                        {!isCenter && (
                          <ProFormText
                            labelCol={{ span: 42 }}
                            width={260}
                            name="name"
                            label="名称"
                            fieldProps={{ maxLength: 4, showCount: true }}
                            rules={[{ required: true, message: '请输入导航名称' }]}
                          />
                        )}
                        {isCenter ? (
                          <>
                            <ProFormRadio.Group
                              name="linkType"
                              label="链接"
                              options={[
                                { label: '内部链接', value: 'internal' },
                                { label: '外部链接', value: 'external' },
                              ]}
                              initialValue={'internal'}
                            />
                            <Form.Item noStyle shouldUpdate>
                              {(form2) => {
                                const linkType =
                                  form2.getFieldValue(['shopNav', index, 'linkType']) || 'internal';
                                if (linkType === 'external') {
                                  return (
                                    <ProFormText
                                      width={260}
                                      name="link"
                                      placeholder="请输入外部链接，如 https://example.com"
                                      rules={[
                                        { required: true, message: '请输入外部链接' },
                                        {
                                          pattern: /^https?:\/\//i,
                                          message: '链接需以 http:// 或 https:// 开头',
                                        },
                                      ]}
                                    />
                                  );
                                }
                                return (
                                  <ProFormSelect
                                    width={260}
                                    name="link"
                                    valueEnum={systemPageEnum}
                                    rules={[{ required: true, message: '请选择导航链接' }]}
                                  />
                                );
                              }}
                            </Form.Item>
                          </>
                        ) : (
                          <ProFormSelect
                            width={260}
                            name="link"
                            label="链接"
                            valueEnum={systemPageEnum}
                            disabled={index === 0 || index === (tabList?.length || 0) - 1}
                            rules={
                              index === 0 || index === (tabList?.length || 0) - 1
                                ? []
                                : [{ required: true, message: '请选择导航链接' }]
                            }
                          />
                        )}
                      </div>
                    </div>
                  </>
                );
              }}
            </ProFormList>
          );
        }}
      </Form.Item>
    </>
  );
};

export default RudderNavForm;


